
import React, { useEffect, useReducer } from 'react';
import { Button, Row, Col, message, Tree, Menu, Spin, Modal } from 'antd';
import { Card, Dropdown } from '@/components/PAntd';
import Ellipsis from '@/components/Ellipsis';
import ClassificationDetail from './components/ClassificationDetail';
import ModelDetail from './components/ModelDetail';
import { connect } from 'dryad';
import styles from './index.less';

const { TreeNode } = Tree;
const { SubMenu } = Menu;

const addChild = (treeData, pid, data) => {
  treeData.forEach((item, index) => {
    if (item.orgId + '' === pid + '') {
      treeData[index].children = data;
    } else if (treeData[index].children) {
      addChild(treeData[index].children, pid, data);
    }
  });
  return treeData;
};

const getChildren = (treeData, pid, rs) => {
  for (let i = 0; i < treeData.length; i++) {
    const item = treeData[i];
    if (item.nodeId + '' === pid + '') {
      rs = item.children.map(v => v.nodeId);
      return rs;
    }

    if (item.isModel + '' === '0' && item.children) {
      return getChildren(item.children, pid, rs);
    }
  }
};

// const mapStateToProps = ({ organizationManage, loading }) => ({
//   organizationManage,
// });

// 定义初始值
const initialState = {
  treeData: [{ orgName: '全部', level: 0, orgId: 0 }],
  selectedKeys: undefined,
  type: 'edit',
  loadedKeys: [],
  tableData:[],
};

// 定义reducer函数，参数为state值和action动作,想要改变state的特定值的操作都放在reducer中
function reducer(state, action) {
  const { method = 'setState', ...rest } = action;
  switch (method) {
    case 'setState':
      return { ...state, ...rest };
    default:
      throw new Error();
  }
}

const OrganizationManage = props => {
  // 初始化userReducer,参数为定义好的reducer函数和initialState（初始状态值）
  const [state, setState] = useReducer(reducer, initialState);
  const { treeData, selectedKeys, type, loadedKeys, tableData, orgId } = state;

  const { dispatch, loading } = props;

  // 组件挂载时加载初始数据
  useEffect(() => {
    // 加载根节点的子节点
    findChildByParentId(0);
  }, []);

  const findChildByParentId = (pid = 0) => {
    // 资源分类树最多添加5层分类；超过5层系统提示“最多支持5层分类
    dispatch({
      type: 'organizationManage/findChildByParentId',
      payload: { pid },
      callback: childByParentId => {
        try {
          setState({ loadedKeys: [] });
          const newTreeData = addChild(treeData, pid, childByParentId || []);
          setState({ treeData: [...newTreeData] });
        } catch (error) {
          console.error('更新树数据失败:', error);
          message.error('加载数据失败，请重试');
        }
      },
    });
  };

  const findModelAttribute = modelId => {
    // 资源分类树最多添加5层分类；超过5层系统提示“最多支持5层分类
    dispatch({
      type: 'organizationManageNew/findModelAttribute',
      payload: { modelId, page: 1, limit: 9999 },
    });

    dispatch({
      type: 'organizationManageNew/findModelDetail',
      payload: { modelId },
    });
  };

  const addChildNode = value => {
    if (type === 'add') {
      return new Promise(resolve => {
        dispatch({
          type: 'organizationManageNew/addChildNode',
          payload: {
            ...value
            // pid: value.pid,
            // addString: JSON.stringify([value]),
          },
          callback: (res) => {
            if(res.code == 200){
              message.success(res.msg)
              findChildByParentId(value.pid);
              // setState({
              //   selectedKeys:
              // })
              // this.setState({
              //   type:'edit'
              // })
              resolve();
              // setTimeout(()=>{
              //   this.child.isChange()
              //
              // },100)

            }else{
              message.error(res.msg)
            }

          },
        });
      });
    }

    return new Promise(resolve => {
      dispatch({
        type: 'organizationManageNew/updateNode',
        payload: value,
        callback: (res) => {
          if(res.code == 200){
            message.success(res.msg)
            findChildByParentId(value.pid);
            // if (value.isModel * 1.0) {
            // findModelAttribute(value.modelId);
            // findChildByParentId(value.orgId);
            // }
            resolve();
          }else{
            message.error(res.msg)
          }


        },
      });
    });
  };

  const handleAddMenu = (item, key) => {
    const node = {
      pid: item.orgId,
      pname: item.orgName,
      level: item.level * 1.0 + 1,
      // leaderId: item.leaderId,
      // leaderName: item.leaderName
    };

    if (key === '添加分类') {
      setState({
        type: 'add',
        selectedKeys: { ...node},
        tableData:[],
      });
      return;
    }
    // if (key === '关联模型') {
    //   setState({
    //     type: 'add',
    //     selectedKeys: { ...node, isModel: 1 },
    //   });
    //   return;
    // }
  };

  const handleMoreMenu = (item, key) => {
    const children = getChildren(treeData, item.pid, '');
    const index = children.indexOf(item.orgId);

    // 调整顺序，arr 数组，indexAdd 添加元素的位置，indexDel 删除元素的位置（indexAdd与indexDel交换位置）
    function handleChangeOrder(arr, indexAdd, indexDel) {
      arr[indexAdd] = arr.splice(indexDel, 1, arr[indexAdd])[0];
      return arr;
    }

    let nodeString = [];
    if (key === '上移') {
      if (index - 1 >= 0) {
        nodeString = handleChangeOrder(children, index, index - 1);
      } else {
        message.warn('已经处于置顶，无法上移');
        return;
      }
    }
    if (key === '下移') {
      if (index + 1 < children.length) {
        nodeString = handleChangeOrder(children, index, index + 1);
      } else {
        message.warn('已经处于置底，无法下移');
        return;
      }
    }

    dispatch({
      type: 'organizationManageNew/updateNodeSort',
      payload: { pid: item.pid, nodeString: nodeString.join(',') },
      callback: () => {
        findChildByParentId(item.pid);
      },
    });
  };
  const handleDelete = ({ orgId, pid, isModel }) => {
    Modal.confirm({
      title: '删除确认',
      content: `删除该组织信息可能导致某些特定流程工单失效，是否确定删除?`,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        dispatch({
          type: 'organizationManageNew/deleteChildNode',
          payload: { orgId },
          callback: () => {
            findChildByParentId(pid);
          },
        });
      },
    });
  };

  const onLoadData = treeNode =>
    new Promise(resolve => {
      if (treeNode.props.children) {
        resolve();
        return;
      }

      const level = treeNode.props.dataRef.level * 1.0 + 1;
      if (level > 5) {
        // message.warn('最多支持5层分类');
        resolve();
        return;
      }
      const pid = treeNode.props.dataRef.orgId;
      dispatch({
        type: 'organizationManageNew/findChildByParentId',
        payload: { pid },
        callback: childByParentId => {
          // console.log(childByParentId);
          treeNode.props.dataRef.children = childByParentId;
          setState({ treeData: [...treeData] });
          resolve();
        },
      });
    });
  const changeSelectKeys = (selectedKeys) => {
    setState({
      selectedKeys
    })
  }
  const renderTreeNodes = data => {
    return data.map(item => {
      const addMenu = (
        <Menu
          onClick={e => {
            e.domEvent.stopPropagation();
            handleAddMenu(item, e.key);
          }}
        >
          {item.level * 1.0 < 5 && (
            <Menu.Item key="添加分类" disabled={item.level * 1.0 >= 5}>
              添加分类
            </Menu.Item>
          )}
          {/*<Menu.Item key="关联模型">关联模型</Menu.Item>*/}
        </Menu>
      );

      const moreMenu = (
        <Menu
          onClick={e => {
            e.domEvent.stopPropagation();
            handleMoreMenu(item, e.key);
          }}
        >
          <Menu.Item key="上移">上移</Menu.Item>
          <Menu.Item key="下移">下移</Menu.Item>
          <SubMenu title="复制">
            <Menu.Item key="当前分类">当前分类</Menu.Item>
            <Menu.Item key="包括分支">包括分支</Menu.Item>
          </SubMenu>
        </Menu>
      );
      const title = (
        <div className={styles.treeTitle1}>
          <Ellipsis tooltip length={15}>
            {/*{item.isModel * 1.0 === 0 ? item.orgName : item.orgName}*/}
            {item.orgName}
          </Ellipsis>
          {selectedKeys && selectedKeys.orgId + '' === item.orgId + '' && (
            <div className={styles.actions}>
              {item.orgId !== 0 && (
                <Button
                  type="link"
                  icon="minus-circle"
                  size="small"
                  onClick={e => {
                    e.stopPropagation();
                    handleDelete(item);
                  }}
                ></Button>
              )}

              {item.level * 1.0 <= 4 && (
                <Dropdown overlay={addMenu} placement="bottomCenter" arrow>
                  <Button type="link" icon="plus-circle" size="small"></Button>
                </Dropdown>
              )}
              {/*{item.orgId !== 0 && (*/}
              {/*  <Dropdown overlay={moreMenu} placement="bottomCenter" arrow>*/}
              {/*    <Button type="link" icon="more" size="small"></Button>*/}
              {/*  </Dropdown>*/}
              {/*)}*/}
            </div>
          )}
        </div>
      );
      if (item.children) {
        return (
          <TreeNode
            title={title}
            key={item.orgId}
            dataRef={item}
            // isLeaf={!!(item.isModel * 1.0)}
            blockNode={true}
          >
            {renderTreeNodes(item.children)}
          </TreeNode>
        );
      }
      return (
        <TreeNode
          blockNode={true}
          {...item}
          title={title}
          key={item.orgId}
          dataRef={item}
          // isLeaf={!!(item.isModel * 1.0)}
        />
      );
    });
  };

  const handleSelectKey = (selectedKeys, e) => {
    const dataRef = e.node.props.dataRef;
    console.log('选中节点数据:', dataRef);
    
    // 一次性更新所有状态，避免多次setState导致的渲染问题
    setState({ 
      selectedKeys: dataRef, 
      type: 'edit',
      tableData: dataRef.orgUsers || [], // 直接设置数据，避免异步问题
      orgId: dataRef.orgId
    });
  };

  const handleChangeType = type => {
    setState({ type });
  };

  const handleLoadKeys = loadedKeys => {
    setState({ loadedKeys });
  };

  return (
    <Spin spinning={loading?.effects?.['organizationManageNew/findChildByParentId'] || false}>
      <div className={styles.resourceClassification}>
        <Row gutter={8} style={{overflow:'hidden'}}>
          <Col span={5}>
            <Card title="组织分类" bodyStyle={{ overflow: 'auto', height: 614}}>
              <Tree
                showLine={true}
                loadData={onLoadData}
                onSelect={handleSelectKey}
                loadedKeys={loadedKeys}
                onLoad={handleLoadKeys}
              >
                {renderTreeNodes(treeData)}
              </Tree>
            </Card>
          </Col>
          <Col span={19}>
            {selectedKeys && selectedKeys.orgId !== 0 && (
              selectedKeys.isModel * 1.0 ? (
                <ModelDetail
                  selectedKeys={selectedKeys}
                  type={type}
                  onchangeType={handleChangeType}
                  addChildNode={addChildNode}
                />
              ) : (
                <ClassificationDetail
                  selectedKeys={selectedKeys}
                  type={type}
                  onchangeType={handleChangeType}
                  addChildNode={addChildNode}
                  tableData={tableData || []}
                  orgId={orgId}
                  changeSelectKeys={changeSelectKeys}
                />
              )
            )}
          </Col>
        </Row>
      </div>
    </Spin>
  );
};


export default connect(({ organizationManageNew }) => ({
  organizationManageNew,
}))(OrganizationManage);
