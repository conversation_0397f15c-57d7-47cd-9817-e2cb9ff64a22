/*
 * @Author: ss
 * @Date: 2025-05-16 16:58:57
 * @LastEditors: @LastEditedBy
 * @LastEditTime: 2025-07-30 15:39:07
 * @FilePath: /水利厅运维/src/pages/SysManage/components/transFormModel.js
 */
import React, { Component } from 'react';
import { connect } from 'dryad';
import { Button, Form, message, Upload, Modal } from 'antd';

const FormItem = Form.Item;

@connect(({ resourceManagementOrganization, loading }) => ({
  resourceManagementOrganization,
  loading: loading.effects['resourceManagement/importCi'],
}))
@Form.create()
export default class TransFormModel extends Component {
  state = {
  };

  componentDidMount() {

  }



  render() {
    const {  visible } = this.state;
    const {
      // loading
      handleClose,
    } = this.props;



    return (
      <div>1111</div>
    );
  }
}
