import React, {Component, Fragment} from 'react';
import { connect } from 'dryad';
import {Button, Form, Input, Modal, Select, Table,message} from 'antd';
import { Card } from '@/components/PAntd';
import StandardTable from "@/components/StandardTable";
import TransFormModel from "@/pages/SysManage/components/transFormModel";
import SelectedResource from "@/pages/SysManage/components/selecteResource";
import {decrypt} from '@/utils/EncryptDecryp'
import {hyposensitization,desensitizeEmail} from '@/utils/utils'
  
// import styles from './index.less';

const { TextArea } = Input;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 10 },
  },
};

@Form.create()
@connect(({ organizationManageNew, loading }) => ({
  organizationManageNew,
}))
export default class ClassificationDetail extends Component {

  state = {
    type: this.props.type,
    selectedRows:[],
    defaultPagination: {
      pageSize: 10,
      current: 1,
    },
    visible:false,
    modalList: [],
    tableData:this.props.tableData,
    // tableData:this.props.selectedKeys.orgUsers,
    deleteData:[],
    addData:[],
    selectedKeys:this.props.selectedKeys,
  };

  componentWillReceiveProps(nextProps) {
    const { form } = this.props;
    const { selectedKeys } = nextProps;
    if (
      nextProps.type !== this.props.type ||
      JSON.stringify(nextProps.selectedKeys) !== JSON.stringify(this.props.selectedKeys)
    ) {
      this.setState({ type: nextProps.type,selectedKeys:nextProps.selectedKeys });
      // form.setFieldsValue({
      //   pName: selectedKeys.levels * 1.0 === 1 ? '全部' : selectedKeys.pname,
      //   nodeName1: selectedKeys.nodeName,
      //   levels: selectedKeys.levels,
      //   nodeDes: selectedKeys.nodeDes,
      // });
    }


    if (nextProps.tableData !== this.props.tableData){
      this.setState({
        tableData: nextProps.tableData,
        defaultPagination: {
          pageSize: 10,
          current: 1,
        },
      })
    }
  }
  // componentDidMount() {
  //   this.props.onRef(this)
  // }

  handleOk = () => {
    const { addChildNode, form, selectedKeys, dispatch,orgId,changeSelectKeys} = this.props;
    const {tableData} = this.state;
    let arr = [];
    form.validateFieldsAndScroll((err, values) => {
      if (err) return;
      tableData.map(v => {
        arr.push(v.userId || v.id)
      })
      const param = {
        orgName:values.departmentName,
        leaderId:values.loader,
        userIds:arr.toString(),
        level:selectedKeys.level,
        pid:selectedKeys.pid,
        orgId:selectedKeys.orgId,
      }
      let selectedKeys1 = selectedKeys;
      selectedKeys1.orgName = values.departmentName;
      selectedKeys1.leaderId = values.loader;

      if(addChildNode){
        addChildNode(param)
        this.setState({
          type:'edit'
        })
        changeSelectKeys(selectedKeys1)
      }

    });
    // form.validateFieldsAndScroll(async function(err, values) {
    //   if (err) return;
    //
    //   // const param = { ...selectedKeys, ...values, nodeName: values.nodeName1 };
    //   // if (addChildNode) {
    //   //   await addChildNode(param);
    //   //   that.setState({ type: 'edit' });
    //   //   // that.setState({ type: '' });
    //   // }
    // });
  };
  // isChange = () => {
  //   this.setState({
  //     type:'edit'
  //   })
  // }
  handleSure = () => {
    const { modalList,tableData } = this.state;

    let arr = [];
    // let modalList1 = JSON.parse(JSON.stringify(modalList));
    modalList.map(v => {
      arr.push(v.id)
    })
    this.setState({
      tableData:[...modalList],
      visible:false,
      selectedRows:[],
      // addData:this.props.tableData.filter(v => arr.indexOf(v.userId) === -1),
    },()=>{console.log(this.state.tableData,'wwwww')})
  }
  handleOpen = () => {
    setTimeout(()=>{
      this.child.handleOpen()
    },100)
    this.setState({
      visible:true
    })
  }
  handleClose = () => {
    this.child.cencle()
    this.setState({
      visible:false
    })

  }
  handleDelete = (record) => {
    const { tableData } = this.state;
    const {  form} = this.props;
    if(record.length == 0){
      message.error('请至少选择一条！')
      return;
    }
    let arr = []
    record.map(v => {
      arr.push(v?.userId || v?.id)
    })
    Modal.confirm({
      title: '删除确认',
      content: `是否确定删除?`,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        this.setState({
          tableData: tableData.filter(v => arr.indexOf(v.userId || v.id) === -1),
          // deleteData: this.props.tableData.filter(v => arr.indexOf(v.userId) !== -1)
          selectedRows: [],
        })
        form.setFieldsValue({
          loader:''
        })
      },
    });


  }
  handleCancel = () => {
    const { form, type, onchangeType,selectedKeys,handleSelectKey } = this.props;
    // handleSelectKey()
    form.resetFields();
    const newType = type === 'add' ? '' : 'edit';
    this.setState({ type: newType,tableData:this.props.tableData });
    if (onchangeType) onchangeType(newType);
  };
  columns = [
    {
      title: '用户名称',
      key: 'username',
      dataIndex: 'username',
      width: 150,
      align: 'center',
    },
    {
      title: '联系方式',
      key: 'phonenum',
      dataIndex: 'phonenum',
      width: 150,
      align: 'center',
      render: (text, record) => {
        return hyposensitization(decrypt(text));
      }
    },
    {
      title: '邮箱',
      key: 'email',
      dataIndex: 'email',
      width: 150,
      align: 'center',
      render: (text, record) => {
        return desensitizeEmail(decrypt(text));
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      align: 'center',
      render: (text, record) => {
        return (
          <Fragment>
            <a
              onClick={() => {
                this.handleDelete([record]);
              }}
            >
              删除
            </a>
          </Fragment>
        );
      },
    },
  ];
  columns1 = [
    {
      title: '用户名称',
      key: 'username',
      dataIndex: 'username',
      width: 150,
      align: 'center',
    
    },
    {
      title: '联系方式',
      key: 'phonenum',
      dataIndex: 'phonenum',
      width: 150,
      align: 'center',
      render: (text, record) => {
        return hyposensitization(decrypt(text));
      }
    },
    {
      title: '邮箱',
      key: 'email',
      dataIndex: 'email',
      width: 150,
      align: 'center',
      render: (text, record) => {
        return desensitizeEmail(decrypt(text));
      }
    },
  ]
  handleSelectRows = rows => {
    this.setState({
      selectedRows: rows,
    });
  };
  handlePaginationTable = pagination => {
    this.handleSearch({ pagination });
  };
  handleSearch = (params = {}) => {
    // if (!selectedKeys && params.length === 0) return;
    const { defaultPagination } = this.state;
    const { pagination = {}, tags, ...rest } = params;
    const newPagination = {
      ...defaultPagination,
      ...pagination,
    };

    this.setState({ defaultPagination: newPagination });

    // const search = tags ? JSON.stringify(tags.map(v => ({ [v.key]: v.value }))) : '';
    // if (tags) {
    //   this.setState({ selectedRows: [], search });
    // }

    // const param = {
    //   ...selectedKeys,
    //   sidx: '',
    //   sord: '',
    //   page: newPagination.current,
    //   limit: newPagination.pageSize,
    //   type: selectedKeys && selectedKeys.modelType ? selectedKeys.modelType : params.modelType,
    //   search,
    //   ...rest,
    // };
    // dispatch({
    //   type: 'resourceManagement/findCiPager',
    //   payload: param,
    // });
  };
  setSelectValue = (value) => {
    let { modalList,tableData } = this.state;
    modalList = [...value];

    // let arr = [];
    // modalList.map(v => {
    //   arr.push(v.id)
    // })
    this.setState({
      // modalList:tableData.length == 0? modalList : tableData.filter(v => arr.indexOf(v.userId) === -1)
      modalList
    });
  };
  onRef = (ref) => {
    this.child = ref
  }
  render() {
    const { type,selectedRows,defaultPagination,visible,modalList,tableData = [],selectedKeys = {}, } = this.state;
    const {
      form: { getFieldDecorator },
    } = this.props;
    const disabled = type === 'add';
    if (!type) return null;
    return (
      <Card title={disabled ? '组织设置' : '组织详情'} bodyStyle={{ overflow: 'auto', height: 614,}}>
        {disabled ? (
          <div>
            <Button
              type="primary"
              htmlType="submit"
              onClick={() => {
                this.handleOk();
              }}
            >
              保存
            </Button>
            <Button
              htmlType="submit"
              style={{ marginLeft: '20px' }}
              onClick={() => {
                this.handleCancel();
              }}
            >
              取消
            </Button>
          </div>
        ) : (
          <Button
            type="primary"
            htmlType="submit"
            onClick={() => {
              this.setState({ type: 'add' });
            }}
          >
            编辑
          </Button>
        )}
        <Form {...formItemLayout} onSubmit={this.handleSubmit}>
          <Form.Item label="部门名称">
            {getFieldDecorator('departmentName', {
              initialValue: selectedKeys.orgName,
              rules: [
                {
                  required: true,
                  message: '请输入部门名称',
                },
                {
                  max: 100,
                  message: '分类名称不能超过100字符',
                },
              ],
            })(<Input placeholder="请输入"  disabled={!disabled}/>)}
          </Form.Item>
          <Form.Item label="父级部门">
            {getFieldDecorator('fatherDepartment', {
              initialValue: selectedKeys.level * 1.0 === 1 ? '全部' : selectedKeys.pname,
              // initialValue: selectedKeys.levels * 1.0 === 1 ? '全部' : '',
              rules: [
                {
                  required: true,
                  message: '请输入父级部门',
                },
              ],
            })(<Input placeholder="请输入"  disabled/>)}
          </Form.Item>
          <Form.Item label="部门领导">
            {getFieldDecorator('loader', {
              initialValue: selectedKeys.leaderId,
              rules: [
                {
                  required: true,
                  message: '请选择部门领导',
                },
              ],
            })(
              <Select
                // style={{ width: 300 }}
                placeholder="请选择"
                allowClear
                disabled={!disabled}
                // showSearch
                // filterOption={(input, option) =>
                //   option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                // }
              >
                {

                  tableData.length != 0 ? tableData.map(item => (
                    <option value={item?.userId || item?.id}>{item?.username}</option>
                  )):null
                }
              </Select>
            )}
          </Form.Item>
        </Form>
        {
          disabled?
            (<div style={{marginBottom:'10px'}}>
              <Button
                onClick={() => {
                  this.handleOpen();
                }}
              >
                添加成员
              </Button>
              <Button
                style={{ marginLeft: '20px' }}
                onClick={() => {
                  this.handleDelete(selectedRows);
                }}
              >
                删除
              </Button>
            </div>)
            :
            null
        }
        {
          disabled ? (<StandardTable
            size='small'
              tableAlert={false}
              // loading={!!loading}
              data={{
                list: tableData,
                pagination: { showSizeChanger: false, ...defaultPagination },
              }}
              columns={this.columns}
              selectedRows={selectedRows}
              // selectedRowsKeys={selectedRowsKeys}
              onSelectRow={this.handleSelectRows}
            onChange={this.handlePaginationTable}
            scroll={{ y: 260 }}
            ></StandardTable>):
            (
              <Table columns={this.columns1} dataSource={tableData} onChange={this.handlePaginationTable} />
            )
        }

        <Modal
          title="添加成员"
          visible={visible}
          onOk={() => {
            this.handleSure();
          }}
          // confirmLoading={loading||false}
          onCancel={this.handleClose}
          width={'80%'}
        >
          <SelectedResource
            // resourceModuleList={[...tableData]}
            onRef={this.onRef}
            setSelectValue={this.setSelectValue}
            resourceList={[...tableData]}
            modalList={modalList}
          />
        </Modal>

      </Card>
    );
  }
}
