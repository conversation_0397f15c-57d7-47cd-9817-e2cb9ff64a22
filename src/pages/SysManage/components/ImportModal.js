import React, { Component } from 'react';
import { connect } from 'dryad';
import { Button, Form, message, Upload, Modal } from 'antd';
import Debounce from 'lodash-decorators/debounce';
import Bind from 'lodash-decorators/bind';
const FormItem = Form.Item;

@connect(({ resourceManagementOrganization, loading }) => ({
  resourceManagementOrganization,
  loading: loading.effects['resourceManagementOrganization/importCi'],
}))
@Form.create()
export default class ImportModal extends Component {
  state = {
    visible: false,
    modelName: '',
    fileList: [],
  };

  componentDidMount() {}


  @Bind()
  @Debounce(400)
  handleOk = () => {
    const { fileList } = this.state;
    const { handleSearch, form, dispatch, selectedKeys } = this.props;

    form.validateFields(err => {
      if (err) return;

      const formData = new FormData();
      formData.append('file', fileList[0]);
      formData.append('type', selectedKeys.modelType);
      formData.append('modelId', selectedKeys.modelId);
      formData.append('nodeId', selectedKeys.nodeId);

      dispatch({
        type: 'resourceManagementOrganization/importCi',
        payload: formData,
        callback: (data) => {
          handleSearch();
          this.setState({
            fileList: [],
            visible: false,
          });
          // form.resetFields()
        },
      });
    });
  };

  handleCancel = () => {
    const { form } = this.props
    // if(form) form.resetFields()
    this.setState({
      visible: false,
      modelName: '',
      fileList: [],
    });
  };

  handleDownLoad = () => {
    const {
      resourceManagement: { exportCITemplate },
      selectedKeys,
    } = this.props;

    window.location.href = `${exportCITemplate}?type=${selectedKeys.modelType}&modelId=${selectedKeys.modelId}`;
  };

  checkFile = (rule, value, callback) => {
    if (!value) {
      callback('导入文件不能为空');
      return;
    }
    const { file, fileList } = value;
    if (file.size > 50 * 1024 * 1024) {
      callback('文件大小不超过50M');
    }

    if (fileList.length <= 0) {
      callback('导入文件不能为空');
      return;
    }
    callback();
  };

  render() {
    const { visible, fileList } = this.state;
    const {
      form: { getFieldDecorator, resetFields },
      disabled,
      loading
    } = this.props;


    const props = {
      fileList: fileList,
      multiple: false,
      onRemove: () => {
        this.setState({
          fileList: [],
        });
        resetFields('file');
      },
      beforeUpload: file => {
        if (file.size > 50 * 1024 * 1024) {
          cancelUpload();
          message.warning('文件大小不超过50M');
          return false;
        }
        this.setState({
          fileList: [file],
        });
        return false;
      },
    };

    return (
      <div style={{ display: 'inline-block', marginLeft: '10px' }}>
        <Button
          disabled={disabled}
          onClick={() => {
            this.setState({ visible: true });
            // this.handleSearch();
          }}
        >
          导入
        </Button>
        <Modal
          title="导入设备"
          visible={visible}
          onOk={() => {
            this.handleOk();
          }}
          confirmLoading={loading||false}
          onCancel={this.handleCancel}
        >
          <Form layout="inline">
            <FormItem label="导入设备：" extra="支持扩展名：xls、xlsx">
              {getFieldDecorator('file', {
                rules: [
                  {
                    validator: this.checkFile,
                  },
                ],
              })(
                <Upload {...props} accept=".xlsx,.xls">
                  <Button disabled={fileList.length > 0}>上传文件</Button>
                </Upload>,
              )}
            </FormItem>

            <FormItem>
              <Button type="link" onClick={this.handleDownLoad}>
                模板下载
              </Button>
            </FormItem>
          </Form>
        </Modal>
      </div>
    );
  }
}
