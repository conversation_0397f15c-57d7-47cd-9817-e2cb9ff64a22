
import {
  findChildByParentId,
  findUserPager,
  deleteChildNode,
  updateNode,
  addChildNode
} from '@/services/sysManage/organizationManage';
import { message } from 'antd';

const defaultState = {
  modelAttribute: {
    data: [],
    total: 0,
  },
  modelsPager: {
    data: [],
    total: 0,
  },
  modelDetail:{}
};

export default {
  namespace: 'organizationManageNew',

  state: defaultState,

  effects: {
    *findChildByParentId({ payload, callback }, { call, put }) {
      const response = yield call(findChildByParentId, payload);
      if (!response) return;
      if (callback) callback(response.resData || []);
    },
    *findUserPager({ payload, callback }, { call, put }) {
      const response = yield call(findUserPager, payload);
      if (!response) return;
      if (callback) callback(response || []);
    },
    *deleteChildNode({ payload, callback }, { call, put }) {
      const response = yield call(deleteChildNode, payload);
      if (!response) return;
      if (callback) callback(response || []);
    },
    *updateNode({ payload, callback }, { call, put }) {
      const response = yield call(updateNode, payload);
      if (!response) return;
      if (callback) callback(response || []);
    },
    *addChildNode({ payload, callback }, { call, put }) {
      const response = yield call(addChildNode, payload);
      if (!response) return;
      if (callback) callback(response || []);
    },

  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
    clear(state, action) {
      return {
        ...state,
        ...defaultState,
        ...action.payload,
      };
    },
  },
};
