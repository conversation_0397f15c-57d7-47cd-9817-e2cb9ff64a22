import {
  findChildByParentIdAndRoleId,
  findCiPager,
  findAttributeByType,
  addCi,
  deleteCi,
  findCiDetailByCiid,
  editCi,
  exportCITemplate,
  importCi,
  exportErrorData,
  exportCi,
} from '@/services/resourceCenter/resourceManagement';
import { objToParam } from '@/utils/utils';
import { message } from 'antd';

const defaultState = {
  // tags: [],

  ciPager: {
    data: [],
    total: 0,
  },
  attributeByType: [],
  ciDetailByCiid: [],
  exportCITemplate,
};

export default {
  namespace: 'resourceManagementOrganization',

  state: defaultState,

  effects: {
    *findChildByParentIdAndRoleId({ payload, callback }, { call, put }) {
      const response = yield call(findChildByParentIdAndRoleId, payload);
      if (!response) return;
      if (callback) callback(response.resData || []);
    },
    *findCiPager({ payload, callback }, { call, put }) {
      const response = yield call(findCiPager, payload);
      yield put({
        type: 'save',
        payload: {
          ciPager: response.resData || {},
        },
      });
    },
    *findAttributeByType({ payload, callback }, { call, put }) {
      const response = yield call(findAttributeByType, payload);
      yield put({
        type: 'save',
        payload: {
          attributeByType: response.resData || [],
        },
      });
    },
    *addCi({ payload, callback }, { call, put }) {
      const response = yield call(addCi, payload);
      if (response && response.code === 200) {
        message.success(response.msg);
        if (callback) callback();
      } else {
        message.error(response.msg);
      }
    },
    *deleteCi({ payload, callback }, { call, put }) {
      const response = yield call(deleteCi, payload);
      if (response && response.code === 200) {
        message.success(response.msg);
        if (callback) callback();
      } else {
        message.error(response.msg);
      }
    },
    *findCiDetailByCiid({ payload, callback }, { call, put }) {
      const response = yield call(findCiDetailByCiid, payload);
      yield put({
        type: 'save',
        payload: {
          ciDetailByCiid: response.resData || [],
        },
      });
      if (callback) callback(response.resData || []);
    },
    *editCi({ payload, callback }, { call, put }) {
      const response = yield call(editCi, payload);
      if (response && response.code === 200) {
        message.success(response.msg);
        if (callback) callback();
      } else {
        message.error(response.msg);
      }
    },
    *importCi({ payload, callback }, { call, put }) {
      const response = yield call(importCi, payload);

      if (!response) {
        return;
      }

      const msgs = {
        error: '文件解析错误',
        null: '无数据导入',
        typeError: '模型类型错误',
        noEditColError: '无可编辑属性',
        colRepeatError: '导入属性重复',
        columnsError: '导入属性不符',
        dbError: '数据库更新报错',
        ciidRepeat: '导入文件存在重复配置项编号',
      };

      const msg = response.resData;
      if (msgs[msg]) {
        message.error(msgs[msg]);
        return;
      }

      if (msg.indexOf('success-') !== -1) {
        message.success(msg.split('-')[1]);
        if (callback) callback('success');
        return;
      }
      if (msg.indexOf('exlVersionTimeout-') !== -1) {
        message.warn('以下配置版本过期：' + msg.split('-')[1]);
        return;
      }

      message.warn(msg.split('-')[0]);
      window.location.href = `${exportErrorData}?fileName=${msg.split('-')[1]}`;
    },
    *exportCi({ payload }, { call, put }) {
      window.location.href = exportCi + objToParam(payload);
    },
    *changefindCiPager({ payload, callback }, { call, put }) {
      // const response = yield call(findCiPager, payload);
      yield put({
        type: 'save',
        payload: {
          ciPager: payload || {},
        },
      });
      if (callback) callback('success');
    },
  },

  reducers: {
    save(state, action) {
      return {
        ...state,
        ...action.payload,
      };
    },
    clear(state, action) {
      return {
        ...state,
        ...defaultState,
        ...action.payload,
      };
    },
  },
};
