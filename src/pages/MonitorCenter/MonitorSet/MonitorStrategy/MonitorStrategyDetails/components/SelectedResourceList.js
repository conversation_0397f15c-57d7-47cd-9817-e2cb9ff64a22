/*
 * @Descripttion:
 * @version:
 * @LastEditors: @LastEditedBy
 * @Date: 2025-04-09 13:23:50
 * @LastEditTime: 2025-07-30 15:09:48
 */
import React, { Component, Fragment } from 'react';
import { connect, createShadow } from 'dryad';
import Button from '@/components/PAntd/PButton';
import StandardTable from '@/components/StandardTable';
import { Form, message, Input, Row, Col, Card, Modal, Radio, Affix } from 'antd';
// import Highlighter from 'react-highlight-words';
import { withRouter } from 'react-router-dom';
import SelectModel from './SelecteModel';
import SelectResource from './selecteResource';
import { onEnterPage, onLeavePage } from '@/utils/openTab';
import { v4 as uuidv4 } from 'uuid';
const { confirm } = Modal;
const defaultPagination = {
  pageSize: 5,
  current: 1,
};
@connect(({ monitorStrategyDetails, loading }) => ({
  monitorStrategyDetails,
  //   loading: loading.effects['CabinetManagement/fetch'],
}))
@Form.create()
class SelectedResource extends Component {
  state = {
    pagination: defaultPagination,
    selectedRows: [],
    visible: false,
    resourceId: '',
    modelId: '',
    valueType: 1,
    resourceList: [],
    searchList: [],
    resourceModuleList: [],
    total: 0,
    add: true,
    searchValue: '',
    showList: [],
    type: 0,
    modalList: [],
    colum: 1,
    select: true,
    sourceType: null,//当前资源类型
  };
  componentDidMount() {
    this.getInitialList();
    this.getAllmodel();
    this.unlistenHistory = onEnterPage(this.props, () => {
      this.getInitialList();
      this.getAllmodel();

    });
  }



  componentWillUnmount() {
    if (this.unlistenHistory) this.unlistenHistory();
  }


  next = () => {
    const { resourceList, valueType } = this.state;
    if (resourceList.length > 0) {
      this.props.getResourceList(resourceList);
      this.props.next(resourceList, valueType);
    } else {
      message.error('请先选择资源对象');
    }
  };
  getInitialList = () => {
    const { dispatch, strategyId } = this.props;
    if (strategyId) {
      dispatch({
        type: 'monitorStrategyDetails/getStrategyRelaModuleInfo',
        payload: {
          current: 1,
          pageSize: 9999,
          id: strategyId,
          param: '',
        },
        callback: response => {
          const resData = response.resData;
          this.setState({
            resourceList: resData.data,
            showList: resData.data,
            modalList: resData.data,
            total: resData.data.length,
            valueType: resData.valueType,
            add: false,
            type: resData.valueType,
            colum: resData.valueType,
          });
          this.props.getResourceList(resData.data);
          let moduleType = '';
          if (resData.data.length > 0 && resData.valueType === 0) {
            let str = resData.data[0].ciId;
            let lastNum = resData.data[0].ciId.substr(-8, 8);
            moduleType = str.replace(lastNum, '');
            this.setState({
              moduleType,
              select: true,
            });
          }
        },
      });
    } else {
      this.setState({
        valueType: 1,
      });
    }
  };
  // 获取所有的模型
  getAllmodel = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'monitorStrategyDetails/getStrategyModuleTypeInfo',
      payload: {
        moduleType: '',
      },
      callback: response => {
        this.setState({
          resourceModuleList: response.resData,
        });
      },
    });
  };
  resourceSearch = () => {
    let { searchValue, resourceList, showList } = this.state;

    if (resourceList.length > 0) {
      if (resourceList[0].ciId) {
        showList = resourceList.filter(item => {
          return item.ip.indexOf(searchValue) > -1;
        });
        this.setState({
          showList,
          total: showList.length,
        });
      }
    }
  };

  modelNameChange = e => {
    let searchValue = e.target.value;
    this.setState({ searchValue });
  };
  modelDel = () => {
    let { selectedRows, resourceList } = this.state;
    confirm({
      title: '删除确定',
      content: '确定要删除吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        let newList = resourceList.filter(item => !selectedRows.some(i => i.ciId === item.ciId));
        message.success('删除成功');
        this.setState({
          resourceList: newList,
          modalList: newList,
          showList: newList,
          selectedRows: [],
          total: newList.length,
          searchValue: '',
        });
        this.setSelectValue(newList);
      },
    });
  };
  del = (type, id) => {
    const { resourceList } = this.state;
    confirm({
      title: '删除确定',
      content: '确定要删除吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        if (type === '0') {
          let newList = resourceList.filter(item => item.ciId !== id);
          message.success('删除成功');
          this.setState({
            resourceList: newList,
            // modalList: newList,
            showList: newList,
            selectedRows: [],
            total: newList.length,
            searchValue: '',
          });
          this.setSelectValue(newList);
        } else {
          let newList = resourceList.filter(item => {
            return item.moduleId !== id;
          });
          message.success('删除成功');
          this.setState({
            resourceList: newList,
            modalList: newList,
            showList: newList,
            selectedRows: [],
            total: newList.length,
            searchValue: '',
          });
          this.setSelectValue(newList);
        }
      },
    });
  };
  //****************表格方法**************** */
  handleSearch = (params = {}) => {
    const { pagination = {}, ...rest } = params;
    const newPagination = {
      ...defaultPagination,
      ...pagination,
    };
    this.setState({ pagination: newPagination });
  };
  handleSelectRows = rows => {
    this.setState({
      selectedRows: rows,
    });
  };
  handlePaginationTable = (pagination, filtersArg, sorter) => {
    this.handleSearch({ pagination });
  };

  /*************************弹窗方法*******************************/
  showModal = () => {
    this.setState({
      visible: true,
    });
  };
  setSelectValue = (value, type) => {
    let { modalList } = this.state;
    modalList = [...value];
    this.setState({
      modalList,
      type,
    });
  };
  handleOk = e => {
    const { type } = this.state;
    const modalList = JSON.parse(JSON.stringify(this.state.modalList));
    let moduleType = '';
    if (modalList.length > 0 && modalList[0].ciId) {
      let str = modalList[0].ciId;
      let lastNum = modalList[0].ciId.substr(-8, 8);
      moduleType = str.replace(lastNum, '');
      this.setState({
        moduleType,
        select: true,
        resourceList: modalList,
        showList: modalList,
        total: modalList.length,
        colum: type,
        visible: false,
        valueType: type,
      });
    } else {
      this.setState({
        resourceList: modalList,
        showList: modalList,
        total: modalList.length,
        colum: type,
        visible: false,
        valueType: type,
      });
    }
  };

  handleCancel = e => {
    const resourceList = JSON.parse(JSON.stringify(this.state.resourceList));
    if (resourceList.length > 0) {
      if (resourceList[0].moduleId) {
        this.setState({
          valueType: 1,
          visible: false,
          modalList: resourceList,
        });
      } else {
        this.setState({
          valueType: 0,
          visible: false,
          modalList: resourceList,
        });
      }
    } else {
      this.setState({
        visible: false,
        modalList: [],
        showList: [],
      });
    }
  };
  // radio方法
  handleTypeChange = e => {
    const { add } = this.state;
    // if (add) {
    // }
    this.setState({ valueType: e.target.value });
  };
  initCo1 = [
    { title: '模型名称', dataIndex: 'moduleName' },
    { title: '模型描述', dataIndex: 'moduleDesc' },
    {
      title: '操作',
      render: record => {
        let id = record.moduleId;
        return (
          <span>
            <a
              onClick={() => {
                this.del('1', id);
              }}
            >
              删除
            </a>
          </span>
        );
      },
    },
  ];

  initCo2 = [
    { title: '资源名称', dataIndex: 'deviceName', key: 'deviceName' },
    { title: '资源IP', dataIndex: 'ip', key: 'ip' },
    {
      title: '操作',
      render: record => {
        const id = record.ciId;
        return (
          <span>
            <a
              onClick={record => {
                this.del('0', id);
              }}
            >
              删除
            </a>
          </span>
        );
      },
    },
  ];

  render() {
    const {
      pagination,
      selectedRows,
      total,
      showList,
      valueType,
      resourceModuleList,
      modalList,
      colum,
      moduleType,
      visible,
      searchValue,
    } = this.state;
    return (
      <Card style={{ height: '100%' }}>
        <Row
          style={{
            marginTop: '10px',
            marginBottom: '15px',
          }}
        >
          <Col span={8}>
            <Button style={{ marginRight: '15px' }} type="primary" onClick={this.showModal}>
              选择
            </Button>
            <Button
              type="default"
              disabled={selectedRows.length === 0}
              onClick={() => this.modelDel()}
              style={{ display: valueType === 1 ? 'none' : '' }}
            >
              删除
            </Button>
          </Col>
          <Col span={16} style={{ textAlign: 'right', display: valueType === 1 ? 'none' : '' }}>
            资源IP：
            <Input
              allowClear
              onChange={e => this.modelNameChange(e)}
              style={{ width: '200px', marginRight: '15px' }}
              placeholder="请输入"
              value={searchValue}
            />
            <Button type="primary" onClick={() => this.resourceSearch()}>
              查询
            </Button>
          </Col>
        </Row>
        <StandardTable
          rowKey="key"
          // rowKey={valueType === 0 ? 'ciId' : 'moduleId'}
          data={{
            list: showList.map(item => {
              return {
                ...item,
                key: item.ciId ?? item.moduleId,
                // key: item[valueType === 0 ? 'ciId' : 'moduleId'],
              };
            }),
            pagination: { ...pagination, total: total, pageSizeOptions: ['5', '10', '20', '30'] },
          }}
          columns={colum === 1 ? this.initCo1 : this.initCo2}
          selectedRows={selectedRows}
          onSelectRow={this.handleSelectRows}
          onChange={this.handlePaginationTable}
        />
        <div
          style={{
            marginTop: 20,
            display: 'flex',
            justifyContent: 'center',
          }}
        >
          <Button
            onClick={() => this.props.prev()}
            type="primary"
            style={{ width: '80px', marginRight: '10px' }}
          >
            上一步
          </Button>
          <Button onClick={this.next} type="primary" style={{ width: '80px' }}>
            下一步
          </Button>
        </div>
        <Modal
          title="选择资源对象"
          visible={this.state.visible}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          width={1200}
          destroyOnClose={true}
        >
          <Radio.Group value={valueType} onChange={this.handleTypeChange}>
            <Radio.Button value={1}>按照模型选择</Radio.Button>
            <Radio.Button value={0}>按照资源选择</Radio.Button>
          </Radio.Group>
          <div style={{ display: valueType === 1 ? 'block' : 'none' }}>
            <SelectModel
              resourceModuleList={resourceModuleList}
              setSelectValue={this.setSelectValue}
              resourceList={modalList}
            />
          </div>
          <div style={{ display: valueType === 0 ? 'block' : 'none' }}>
            <SelectResource
              setSelectValue={this.setSelectValue}
              resourceModuleList={resourceModuleList}
              resourceList={modalList}
              moduleType={moduleType}
              visible={visible}
            />
          </div>
        </Modal>
      </Card>
    );
  }
}
export default withRouter(SelectedResource);